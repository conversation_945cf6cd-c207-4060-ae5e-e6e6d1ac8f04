import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { Store } from '@ngrx/store';
import { catchError, filter, of, switchMap } from 'rxjs';
import { selectCreditLimitLoansIsLoading } from '../+state';
import { CreditLimitDetailsPageActions } from '../+state/actions';

export const creditLimitLoansGuard: CanActivateFn = () => {
  const store = inject(Store);
  store.dispatch(CreditLimitDetailsPageActions.getCreditLimitLoans());

  return waitForData(store).pipe(
    switchMap(() => {
      return of(true);
    }),
    catchError(() => of(false))
  );
};

const waitForData = (store: Store) => {
  return store
    .select(selectCreditLimitLoansIsLoading)
    .pipe(filter((isLoading) => isLoading));
};
