<ng-container *ngIf="creditLimitLoans$ | async as loans">
  <!-- Table -->
  <ng-container *ngIf="loans.length > 0">
    <div class="table-wrapper table-wrapper-height-370">
      <table
        mat-table
        [dataSource]="loans"
        class="w-full"
        aria-label="Credit limit loans table"
      >
        <!-- Loan ID Column -->
        <ng-container matColumnDef="loanId">
          <th mat-header-cell *matHeaderCellDef class="">
            {{ 'credit-limit-details.loans.tableHeaders.loanId' | transloco }}
          </th>
          <td mat-cell *matCellDef="let loan">
            {{ loan.loanId }}
          </td>
        </ng-container>

        <!-- Loan Reference Column -->
        <ng-container matColumnDef="loan">
          <th mat-header-cell *matHeaderCellDef class="">
            {{ 'credit-limit-details.loans.tableHeaders.loan' | transloco }}
          </th>
          <td mat-cell *matCellDef="let loan" [matTooltip]="loan.loan">
            <div class="truncate">
              {{ loan.loan }}
            </div>
          </td>
        </ng-container>

        <!-- Product Column -->
        <ng-container matColumnDef="product">
          <th mat-header-cell *matHeaderCellDef class="">
            {{ 'credit-limit-details.loans.tableHeaders.product' | transloco }}
          </th>
          <td mat-cell *matCellDef="let loan" [matTooltip]="loan.product">
            <div class="truncate">
              {{ loan.product }}
            </div>
          </td>
        </ng-container>

        <!-- Original Principal Amount Column -->
        <ng-container matColumnDef="originalPrincipalAmount">
          <th mat-header-cell *matHeaderCellDef class="">
            {{
              'credit-limit-details.loans.tableHeaders.originalPrincipalAmount'
                | transloco
            }}
          </th>
          <td mat-cell *matCellDef="let loan" class="text-right">
            {{
              loan.originalPrincipal.value
                | currency: loan.originalPrincipal.currency
            }}
          </td>
        </ng-container>

        <!-- Outstanding Principal Amount Column -->
        <ng-container matColumnDef="outstandingPrincipalAmount">
          <th mat-header-cell *matHeaderCellDef class="">
            {{
              'credit-limit-details.loans.tableHeaders.outstandingPrincipalAmount'
                | transloco
            }}
          </th>
          <td mat-cell *matCellDef="let loan" class="text-right">
            {{
              loan.outstandingPrincipal.value
                | currency: loan.outstandingPrincipal.currency
            }}
          </td>
        </ng-container>

        <!-- Early Repayment Amount Column -->
        <ng-container matColumnDef="earlyRepaymentAmount">
          <th mat-header-cell *matHeaderCellDef class="">
            <div class="flex items-center">
              {{
                'credit-limit-details.loans.tableHeaders.earlyRepaymentAmount'
                  | transloco
              }}
              <tm-tooltip
                [escape]="false"
                [tooltipTemplateMessage]="tooltipTemplate"
              >
                <div #tooltipTemplate>
                  {{
                    'credit-limit-details.loans.tableHeaders.earlyRepaymentAmountHint'
                      | transloco
                  }}
                </div>
              </tm-tooltip>
            </div>
          </th>
          <td mat-cell *matCellDef="let loan" class="text-right">
            {{
              loan.earlyRepayment.value | currency: loan.earlyRepayment.currency
            }}
          </td>
        </ng-container>

        <!-- Interest Rate Column -->
        <ng-container matColumnDef="interestRate">
          <th mat-header-cell *matHeaderCellDef class="">
            {{
              'credit-limit-details.loans.tableHeaders.interestRate' | transloco
            }}
          </th>
          <td mat-cell *matCellDef="let loan" class="text-right">
            {{ loan.interestRate | percentageFormat | removeTrailingZeros }}%
          </td>
        </ng-container>

        <!-- Disbursement Date Column -->
        <ng-container matColumnDef="disbursementDate">
          <th mat-header-cell *matHeaderCellDef class="">
            {{
              'credit-limit-details.loans.tableHeaders.disbursementDate'
                | transloco
            }}
          </th>
          <td mat-cell *matCellDef="let loan" class="">
            <ng-container *ngIf="dateFormat$ | async as dateFormat">
              {{ loan.disbursementDate | date: dateFormat }}
            </ng-container>
          </td>
        </ng-container>

        <!-- Maturity Date Column -->
        <ng-container matColumnDef="maturityDate">
          <th mat-header-cell *matHeaderCellDef class="">
            {{
              'credit-limit-details.loans.tableHeaders.maturityDate' | transloco
            }}
          </th>
          <td mat-cell *matCellDef="let loan" class="">
            <ng-container *ngIf="dateFormat$ | async as dateFormat">
              {{ loan.maturityDate | date: dateFormat }}
            </ng-container>
          </td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef class="">
            {{ 'credit-limit-details.loans.tableHeaders.status' | transloco }}
          </th>
          <td mat-cell *matCellDef="let loan" class="text-center">
            <div
              class="operation-status"
              [ngClass]="'credit-limit-status-' + loan.status | lowercase"
            >
              {{ 'e2e.operationStatuses.' + loan.status | transloco }}
            </div>
            <div class="text-compass-60">
              {{ 'e2e.operationStatuses.' + loan.statusReason | transloco }}
            </div>
          </td>
        </ng-container>

        <!-- Header and Row Definitions -->
        <tr
          mat-header-row
          *matHeaderRowDef="displayedColumns; sticky: true"
        ></tr>
        <tr mat-row *matRowDef="let loan; columns: displayedColumns"></tr>
      </table>
    </div>
  </ng-container>

  <!-- Empty state -->
  <tm-no-results
    class="mt-10 block"
    *ngIf="loans.length === 0"
    [showDivider]="false"
  ></tm-no-results>
</ng-container>
