import { HttpClient, HttpParams } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import {
  BorrowerDetail,
  BorrowerDetailResponse,
  BorrowerPortfolio,
  CreditLimitChangeAmount,
  CreditLimitCollateral,
  CreditLimitEarlyRepaymentSimulateRequest,
  CreditLimitOwner,
  FeeTypes,
  LifeInsuranceCoverage,
  LoanDetail,
  LoanEarlyRepaymentFeeSimulate,
  PortfoliosSort,
  sortPortfolios,
} from '@e2e/lib/types';
import { ENVIRONMENT, Environment } from '@tenant-management/lib/environments';
import {
  dateRequestFormat,
  HttpState,
  TmDocument,
  withHttpState,
} from '@tenant-management/lib/types';
import { format } from 'date-fns';
import { map, Observable } from 'rxjs';
import { CollateralNewInsurance } from '../models/collateral-new-insurance';
import { CreditLimitDetails } from '../models/credit-limit-details';
import { CreditLimitLoans } from '../models/credit-limit-loans';
import { CreditLimitPortfolioResponse } from '../models/credit-limit-portfolio-response';
@Injectable({
  providedIn: 'root',
})
export class CreditLimitDetailsService {
  constructor(
    private http: HttpClient,
    @Inject(ENVIRONMENT) private environment: Environment
  ) {}

  getCreditLimitDetails(limitId: any): Observable<CreditLimitDetails> {
    return this.http
      .get<CreditLimitDetails>(
        `${this.environment.apiPrefix}/v1/credit-limits/${limitId}`
      )
      .pipe(
        map((creditLimitDetails: CreditLimitDetails) => {
          return creditLimitDetails;
        })
      );
  }

  uploadCreditLimitDocument(
    limitId: any,
    uploadedDocument: TmDocument
  ): Observable<CreditLimitDetails> {
    return this.http.patch<CreditLimitDetails>(
      `${this.environment.apiPrefix}/v1/credit-limits/${limitId}/documents`,
      uploadedDocument
    );
  }

  getCreditLimitOwner(limitId: any): Observable<CreditLimitOwner> {
    return this.http.get<CreditLimitOwner>(
      `${this.environment.apiPrefix}/v1/customers/${limitId}`
    );
  }

  getCreditLimitOwnerInsurance(
    limitId: any
  ): Observable<HttpState<LifeInsuranceCoverage>> {
    return this.http
      .get<LifeInsuranceCoverage>(
        `${this.environment.apiPrefix}/v1/credit-limits/${limitId}/life-insurance-coverage`
      )
      .pipe(withHttpState());
  }

  getCreditLimitConsumers(
    consumerIdentityIds: string[]
  ): Observable<HttpState<BorrowerDetail[]>> {
    return this.http
      .get<BorrowerDetailResponse>(
        `${this.environment.apiPrefix}/v1/borrowers/${consumerIdentityIds}`
      )
      .pipe(
        map(({ data }) => data),
        withHttpState()
      );
  }

  sendChangeAmount(changeAmount: CreditLimitChangeAmount): Observable<void> {
    return this.http.post<void>(
      `${this.environment.apiPrefix}/v1/requests/change-credit-limit-amount`,
      {
        ...changeAmount,
      }
    );
  }

  cancelCreditLimit(limitId: any): Observable<void> {
    return this.http.post<void>(
      `${this.environment.apiPrefix}/v1/credit-limits/${limitId}/cancel`,
      limitId
    );
  }

  // TODO: reuse common endpoints from credit-limit-details.service and borrower-details.service
  getCreditLimitPortfolios(
    consumerIdentityId: string,
    portfoliosSort: PortfoliosSort
  ): Observable<HttpState<CreditLimitPortfolioResponse>> {
    return this.http
      .get<CreditLimitPortfolioResponse>(
        `${this.environment.apiPrefix}/v1/credit-limits/${consumerIdentityId}/portfolios`
      )
      .pipe(
        map(({ limitId, portfolios }) => ({
          limitId,
          portfolios: sortPortfolios(portfoliosSort, portfolios),
        })),
        withHttpState()
      );
  }

  getCreditLimitPortfolioDetails(
    portfolioId: string
  ): Observable<BorrowerPortfolio> {
    return this.http.get<BorrowerPortfolio>(
      `${this.environment.apiPrefix}/v1/portfolios/${portfolioId}`
    );
  }

  getCreditLimitCollaterals(
    limitId: any
  ): Observable<HttpState<CreditLimitCollateral[]>> {
    return this.http
      .get<CreditLimitCollateral[]>(
        `${this.environment.apiPrefix}/v1/credit-limits/${limitId}/collaterals`
      )
      .pipe(withHttpState());
  }

  createNewInsurance(newInsurance: CollateralNewInsurance) {
    return this.http.post<void>(``, newInsurance);
  }

  getFees(): Observable<any> {
    const filter = `$and(feeType:$in:[${FeeTypes.EarlyRepaymentEarlyNoticeFee};${FeeTypes.EarlyRepaymentOperationalFee};${FeeTypes.EarlyRepaymentCapitalizationFee}])`;
    const params = new HttpParams({
      fromObject: {
        filter,
      },
    });

    return this.http.get<any>(
      `${this.environment.apiPrefix}/v1/fees-service/fees/query`,
      { params }
    );
  }

  getOverview(id: string, effectiveDate: Date, fees: any): Observable<any> {
    const dateRequest = format(effectiveDate as Date, dateRequestFormat);
    const params = new HttpParams({
      fromObject: {
        id,
        effectiveDate: dateRequest,
      },
    });

    const filteredArray = fees.filter(
      (item: any) => item.type !== FeeTypes.EarlyRepaymentOperationalFee
    );

    return this.http.post<void>(
      `${this.environment.apiPrefix}/v1/credit-limits/overview`,
      {
        id,
        effectiveDate: dateRequest,
        fees: filteredArray,
      }
    );
  }

  sendEarlyRepaymentRequest(
    portfolioIds: any,
    earlyRepaymentForm: any,
    fees: any
  ): Observable<any> {
    const formatOptions = 'yyyy-MM-dd';

    // EXCLUDE EARLY_REPAYMENT_OPERATIONAL_FEE
    const filteredArray = fees.filter(
      (item: any) => item.type !== FeeTypes.EarlyRepaymentOperationalFee
    );

    return this.http.post<void>(
      `${this.environment.apiPrefix}/v1/requests/CreditLimitEarlyRepayment/debt-repayment`,
      {
        portfolioIds,
        effectiveDate: format(
          earlyRepaymentForm.effectiveDate as Date,
          formatOptions
        ),
        amount: earlyRepaymentForm.amountToRepay,
        fees: filteredArray,
      }
    );
  }

  sendSimulationRequest(
    earlyRepaymentSimulationRequest: any
  ): Observable<CreditLimitEarlyRepaymentSimulateRequest> {
    return this.http.post<CreditLimitEarlyRepaymentSimulateRequest>(
      `${this.environment.apiPrefix}/v1/requests/CreditLimitEarlyRepayment`,
      {
        ...earlyRepaymentSimulationRequest,
      }
    );
  }

  getFullRepaymentSimulate(
    loanId: string,
    effectiveDate: Date | string,
    fees: LoanEarlyRepaymentFeeSimulate[]
  ): Observable<LoanDetail> {
    // const todayDate = format(new Date(), dateRequestFormat);
    const params: any = {
      loanId,
      fees,
      effectiveDate,
    };

    // if (effectiveDate < todayDate) {
    //   params.effectiveDate = effectiveDate;
    // }

    return this.http.post<LoanDetail>(
      `${this.environment.apiPrefix}/v1/requests/full-early-repayment/simulate`,
      {
        ...params,
      }
    );
  }

  getPartialRepaymentSimulate(
    loanId: string,
    effectiveDate: Date | string,
    fees: LoanEarlyRepaymentFeeSimulate[],
    earlyRepaymentAmount: any,
    spreadMethod: any
  ): Observable<LoanDetail> {
    const params: any = {
      loanId,
      fees,
      effectiveDate,
      earlyRepaymentAmount,
      spreadMethod,
    };

    return this.http.post<LoanDetail>(
      `${this.environment.apiPrefix}/v1/requests/partial-early-repayment/simulate`,
      {
        ...params,
      }
    );
  }

  getDebtRepaymentSimulate(
    limitId: any,
    effectiveDate: any,
    amount: any
  ): Observable<LoanDetail> {
    const params: any = {
      limitId,
      effectiveDate,
      amount,
    };

    return this.http.post<LoanDetail>(
      `${this.environment.apiPrefix}/v1/credit-limits/debt-charge-simulate`,
      {
        ...params,
      }
    );
  }

  getCreditLimitLoans(
    limitId: string
  ): Observable<HttpState<CreditLimitLoans>> {
    const params = {
      limitId,
      effectiveDate: new Date(),
    };

    return this.http
      .post<CreditLimitLoans>(
        `${this.environment.apiPrefix}/v1/credit-limits/debt-charge-simulate`,
        {
          ...params,
        }
      )
      .pipe(withHttpState());
  }
}
