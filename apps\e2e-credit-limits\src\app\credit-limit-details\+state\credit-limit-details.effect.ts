import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { DebtSimulationComponent } from '@e2e/lib/e2e-debt-simulate';
import { PortfolioLoansComponent } from '@e2e/lib/e2e-portfolio-loans';
import { PortfolioProductComponent } from '@e2e/lib/e2e-portfolio-product';
import { RepaymentsSimulationComponent } from '@e2e/lib/e2e-repayments-simulation';
import { E2eService, LoanApplicationsService } from '@e2e/lib/services';
import { E2eLoanApiActions } from '@e2e/lib/state';
import {
  getBorrowerName,
  LoanRepaymentDetails,
  PortfolioLoansDialogData,
} from '@e2e/lib/types';
import { Actions, concatLatestFrom, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import {
  DocumentService,
  LanguageService,
} from '@tenant-management/lib/services';
import {
  selectTenantPortfolioTrackMode,
  TenantManagementLibPageActions,
} from '@tenant-management/lib/state';
import { catchError, filter, map, mergeMap, of, tap } from 'rxjs';
import {
  selectBorrowerCurrentPortfolioId,
  selectBorrowerPortfolioDetails,
  selectBorrowerPrepayments,
  selectConsumer,
  selectConsumerIdentityIds,
  selectCreditLimitId,
  selectCreditLimitOwner,
  selectCustomerId,
  selectedCurrentProductTrackReferenceName,
  selectHasPortfolios,
  selectPortfoliosSort,
  selectProductDetails,
  selectSelectedTrackId,
  selectSnackBarMessage,
} from '.';
import { CreditLimitDetailsService } from '../services/credit-limit-details.service';
import {
  CreditLimitDetailsApiActions,
  CreditLimitDetailsPageActions,
} from './actions';

@Injectable()
export class CreditLimitDetailsEffects {
  constructor(
    private creditLimitDetailsService: CreditLimitDetailsService,
    private loanApplicationsService: LoanApplicationsService,
    private actions$: Actions,
    private store: Store,
    private documentService: DocumentService,
    private router: Router,
    private e2eService: E2eService,
    private dialog: MatDialog,
    private languageService: LanguageService
  ) {}

  getCreditLimitDetails$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.setCreditLimitId),
      concatLatestFrom(() => this.store.select(selectCreditLimitId)),
      mergeMap(([type, limitId]) =>
        this.creditLimitDetailsService.getCreditLimitDetails(limitId).pipe(
          map((creditLimitDetails) => {
            return CreditLimitDetailsApiActions.getCreditLimitDetailsSuccess({
              creditLimitDetails,
            });
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.getCreditLimitDetailsFailure({
                error,
              })
            );
          })
        )
      )
    );
  });

  sendLimitDetailsFilestackDocument$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.sendLimitDetailsFilestackDocument),
      mergeMap(({ type, document }) =>
        this.documentService.sendDocument(document).pipe(
          map((uploadedDocument) => {
            return CreditLimitDetailsApiActions.sendLimitDetailsFilestackDocumentSuccess(
              {
                uploadedDocument,
              }
            );
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.sendLimitDetailsFilestackDocumentFailure(
                {
                  error,
                }
              )
            );
          })
        )
      )
    );
  });

  sendCreditLimitDetailsDocument$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        CreditLimitDetailsApiActions.sendLimitDetailsFilestackDocumentSuccess
      ),
      concatLatestFrom(() => this.store.select(selectCreditLimitId)),
      mergeMap(([{ type, uploadedDocument }, limitId]) =>
        this.creditLimitDetailsService
          .uploadCreditLimitDocument(limitId, uploadedDocument)
          .pipe(
            map((creditLimitDetails) => {
              return CreditLimitDetailsApiActions.sendLimitDetailsDocumentSuccess(
                {
                  creditLimitDetails,
                }
              );
            }),
            catchError((error: unknown) => {
              return of(
                CreditLimitDetailsApiActions.sendLimitDetailsDocumentFailure({
                  error,
                })
              );
            })
          )
      )
    );
  });

  getCreditLimitOwner$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        CreditLimitDetailsPageActions.getCreditLimitOwner,
        CreditLimitDetailsApiActions.getCreditLimitDetailsSuccess
      ),
      concatLatestFrom(() => [
        this.store.select(selectCustomerId),
        this.store.select(selectCreditLimitOwner),
      ]),
      filter(
        ([type, customerId, creditLimitOwner]) =>
          !Object.keys(creditLimitOwner).length
      ),
      mergeMap(([type, customerId]) =>
        this.creditLimitDetailsService.getCreditLimitOwner(customerId).pipe(
          map((creditLimitOwner) => {
            return CreditLimitDetailsApiActions.getCreditLimitOwnerSuccess({
              creditLimitOwner,
            });
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.getCreditLimitOwnerFailure({
                error,
              })
            );
          })
        )
      )
    );
  });

  getCreditLimitOwnerInsurance$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsApiActions.getCreditLimitOwnerSuccess),
      concatLatestFrom(() => this.store.select(selectCreditLimitId)),
      mergeMap(([type, customerId]) =>
        this.creditLimitDetailsService
          .getCreditLimitOwnerInsurance(customerId)
          .pipe(
            map((lifeInsuranceCoverage) => {
              return CreditLimitDetailsApiActions.getCreditLimitOwnerInsuranceSuccess(
                {
                  lifeInsuranceCoverage,
                }
              );
            }),
            catchError((error: unknown) => {
              return of(
                CreditLimitDetailsApiActions.getCreditLimitOwnerInsuranceFailure(
                  {
                    error,
                  }
                )
              );
            })
          )
      )
    );
  });

  getCreditLimitConsumers$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.getCreditLimitConsumers),
      concatLatestFrom(() => [
        this.store.select(selectConsumerIdentityIds),
        this.store.select(selectConsumer),
      ]),
      filter(([type, consumerIdentityIds, consumer]) => !consumer),
      mergeMap(([type, consumerIdentityIds]) =>
        this.creditLimitDetailsService
          .getCreditLimitConsumers(consumerIdentityIds)
          .pipe(
            map((creditLimitConsumers) => {
              return CreditLimitDetailsApiActions.getCreditLimitConsumersSuccess(
                {
                  creditLimitConsumers,
                }
              );
            }),
            catchError((error: unknown) => {
              return of(
                CreditLimitDetailsApiActions.getCreditLimitConsumersFailure({
                  error,
                })
              );
            })
          )
      )
    );
  });

  changeAmount$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.sendChangeAmount),
      mergeMap(({ type, changeAmount }) =>
        this.creditLimitDetailsService.sendChangeAmount(changeAmount).pipe(
          map(() => {
            return CreditLimitDetailsApiActions.sendChangeAmountSuccess();
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.sendChangeAmountFailure({
                error,
              })
            );
          })
        )
      )
    );
  });

  cancelCreditLimit$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.cancelCreditLimit),
      concatLatestFrom(() => this.store.select(selectCreditLimitId)),
      mergeMap(([type, limitId]) =>
        this.creditLimitDetailsService.cancelCreditLimit(limitId).pipe(
          map(() => {
            return CreditLimitDetailsApiActions.cancelCreditLimitSuccess();
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.cancelCreditLimitFailure({
                error,
              })
            );
          })
        )
      )
    );
  });

  navigateToCreditLimits$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(CreditLimitDetailsApiActions.cancelCreditLimitSuccess),
        tap(() => {
          this.router.navigateByUrl('/operations/credit-limits');
        })
      );
    },
    { dispatch: false }
  );

  getCreditLimitPortfolios$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        CreditLimitDetailsPageActions.getCreditLimitPortfolios,
        CreditLimitDetailsPageActions.setCreditLimitCurrentPortfolioId
      ),
      concatLatestFrom(() => [
        this.store.select(selectCreditLimitId),
        this.store.select(selectPortfoliosSort),
        this.store.select(selectHasPortfolios),
      ]),
      filter(([type, borrowerId, portfoliosSort, portfolios]) => !portfolios),
      mergeMap(([type, consumerIdentityId, portfoliosSort]) =>
        this.creditLimitDetailsService
          .getCreditLimitPortfolios(consumerIdentityId, portfoliosSort)
          .pipe(
            map((creditLimitPortfolios) => {
              return CreditLimitDetailsApiActions.getCreditLimitPortfoliosSuccess(
                {
                  creditLimitPortfolios,
                }
              );
            }),
            catchError((error: unknown) => {
              return of(
                CreditLimitDetailsApiActions.getCreditLimitPortfoliosFailure({
                  error,
                })
              );
            })
          )
      )
    );
  });

  getCreditLimitPortfolioDetails$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.getCreditLimitPortfolioDetails),
      concatLatestFrom(() => this.store.select(selectBorrowerPortfolioDetails)),
      filter(
        ([{ portfolioIndex }, portfolioDetails]) =>
          !portfolioDetails[portfolioIndex]
      ),
      mergeMap(([{ portfolioIndex, portfolioId }]) =>
        this.creditLimitDetailsService
          .getCreditLimitPortfolioDetails(portfolioId)
          .pipe(
            map((portfolioDetails) => {
              return CreditLimitDetailsApiActions.getCreditLimitPortfolioDetailsSuccess(
                {
                  portfolioIndex,
                  portfolioDetails,
                }
              );
            }),
            catchError((error: unknown) => {
              return of(
                CreditLimitDetailsApiActions.getCreditLimitPortfolioDetailsFailure(
                  {
                    error,
                  }
                )
              );
            })
          )
      )
    );
  });

  // getCurrentPortfolioDetails$ = createEffect(() => {
  //   return this.actions$.pipe(
  //     ofType(CreditLimitDetailsApiActions.getCreditLimitPortfoliosSuccess),
  //     concatLatestFrom(() => [
  //       this.store.select(selectCurrentPortfolioRequestDetails),
  //       this.store.select(selectCreditLimitPortfoliosIsLoading),
  //     ]),
  //     filter(
  //       ([type, { portfolioId, portfolioIndex }, isLoading]) =>
  //         !isLoading && !!portfolioId && portfolioIndex > -1
  //     ),
  //     map(([type, { portfolioId, portfolioIndex }]) =>
  //       CreditLimitDetailsPageActions.getCreditLimitPortfolioDetails({
  //         portfolioId,
  //         portfolioIndex,
  //       })
  //     )
  //   );
  // });

  getPortfolioCreditLimit$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        CreditLimitDetailsApiActions.getCreditLimitPortfolioDetailsSuccess
      ),
      filter(
        ({ type, portfolioIndex, portfolioDetails }) =>
          !!portfolioDetails.limitId
      ),
      mergeMap(({ type, portfolioIndex, portfolioDetails }) =>
        this.e2eService.getCreditLimit(portfolioDetails.limitId).pipe(
          map((limit) => {
            return CreditLimitDetailsApiActions.getPortfolioLimitSuccess({
              portfolioIndex,
              limit,
            });
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.getPortfolioLimitFailure({
                error,
              })
            );
          })
        )
      )
    );
  });

  getBorrowerPortfolioLoans$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.getBorrowerPortfolioLoans),
      mergeMap(({ type, portfolioId }) =>
        this.e2eService.getPortfolioLoans(portfolioId).pipe(
          map((portfolioLoans) => {
            return CreditLimitDetailsApiActions.getBorrowerPortfolioLoansSuccess(
              {
                portfolioLoans,
              }
            );
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.getBorrowerPortfolioLoansFailure({
                error,
              })
            );
          })
        )
      )
    );
  });

  getBorrowerTrackLoans$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.getTrackLoans),
      mergeMap(({ type, trackId }) =>
        this.e2eService.getTrackLoans(trackId).pipe(
          map((trackLoans) => {
            return CreditLimitDetailsApiActions.getTrackLoansSuccess({
              trackLoans,
            });
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.getTrackLoansFailure({
                error,
              })
            );
          })
        )
      )
    );
  });

  openE2ePortfolioLoansDialog$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(CreditLimitDetailsApiActions.getBorrowerPortfolioLoansSuccess),
        tap(({ type, portfolioLoans }) => {
          this.dialog.open<PortfolioLoansComponent, PortfolioLoansDialogData>(
            PortfolioLoansComponent,
            {
              width: '832px',
              disableClose: true,
              data: {
                loans: portfolioLoans.loans,
                referenceName: portfolioLoans.referenceName,
              },
              direction: this.languageService.getDirection(),
            }
          );
        })
      );
    },
    { dispatch: false }
  );

  openE2eTrackLoansDialog$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(CreditLimitDetailsApiActions.getTrackLoansSuccess),
        concatLatestFrom(() =>
          this.store.select(selectedCurrentProductTrackReferenceName)
        ),
        tap(([{ type, trackLoans }, currentProductTrackReferenceName]) => {
          this.dialog.open<PortfolioLoansComponent, PortfolioLoansDialogData>(
            PortfolioLoansComponent,
            {
              width: '832px',
              disableClose: true,
              // TODO: this needs to be considered how the data should be passed to the dialog
              data: {
                loans: trackLoans.loans,
                referenceName: currentProductTrackReferenceName,
              },
              direction: this.languageService.getDirection(),
            }
          );
        })
      );
    },
    { dispatch: false }
  );

  getBorrowerPortfolioProducts$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        // CreditLimitDetailsPageActions.getBorrowerPortfolioProducts,
        CreditLimitDetailsPageActions.getCreditLimitPortfolioDetails
      ),
      mergeMap(({ type, portfolioId }) =>
        this.e2eService.getProducts(portfolioId).pipe(
          map((portfolioProducts) => {
            return CreditLimitDetailsApiActions.getBorrowerPortfolioProductsSuccess(
              {
                portfolioProducts,
              }
            );
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.getBorrowerPortfolioProductsFailure({
                error,
              })
            );
          })
        )
      )
    );
  });

  getBorrowerPortfolioPrepaymentsProducts$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsApiActions.getBorrowerPortfolioProductsSuccess),
      concatLatestFrom(() =>
        this.store.select(selectBorrowerCurrentPortfolioId)
      ),
      mergeMap(([type, portfolioId]) =>
        this.e2eService.getPrepayments(portfolioId).pipe(
          map((prepayments) => {
            return CreditLimitDetailsApiActions.getBorrowerPortfolioPrepaymentsProductsSuccess(
              {
                prepayments,
              }
            );
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.getBorrowerPortfolioPrepaymentsProductsFailure(
                {
                  error,
                }
              )
            );
          })
        )
      )
    );
  });

  openBorrowerPortfolioProductsDialog$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(CreditLimitDetailsPageActions.getBorrowerPortfolioProducts),
        concatLatestFrom(() => [
          this.store.select(selectProductDetails),
          this.store.select(selectBorrowerPrepayments),
          this.store.select(selectSelectedTrackId),
          this.store.select(selectTenantPortfolioTrackMode),
        ]),
        tap(
          ([
            { type },
            productDetails,
            prepayments,
            trackId,
            tenantPortfolioTrackMode,
          ]) => {
            const { portfolioProducts, portfolioReferenceName } =
              productDetails;
            this.dialog.open(PortfolioProductComponent, {
              width: '832px',
              disableClose: true,
              data: {
                portfolioProducts,
                portfolioReferenceName,
                prepayments,
                trackId,
                tenantPortfolioTrackMode,
              },
              direction: this.languageService.getDirection(),
            });
          }
        )
      );
    },
    { dispatch: false }
  );

  sendDisbursement$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.sendDisbursement),
      mergeMap(({ type, disbursement }) =>
        this.loanApplicationsService.sendDisbursement(disbursement).pipe(
          map(() => {
            return CreditLimitDetailsApiActions.sendDisbursementSuccess();
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.sendDisbursementFailure({
                error,
              })
            );
          })
        )
      )
    );
  });

  sendEffectiveDate$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.sendEffectiveDate),
      mergeMap(({ type, effectiveDate }) =>
        this.loanApplicationsService.sendEffectiveDate(effectiveDate).pipe(
          map(() => {
            return CreditLimitDetailsApiActions.sendEffectiveDateSuccess();
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.sendEffectiveDateFailure({
                error,
              })
            );
          })
        )
      )
    );
  });

  sendChangeEffectiveDate$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.sendChangeEffectiveDate),
      mergeMap(({ type, effectiveDate }) =>
        this.loanApplicationsService
          .sendChangeEffectiveDate(effectiveDate)
          .pipe(
            map(() => {
              return CreditLimitDetailsApiActions.sendChangeEffectiveDateSuccess();
            }),
            catchError((error: unknown) => {
              return of(
                CreditLimitDetailsApiActions.sendChangeEffectiveDateFailure({
                  error,
                })
              );
            })
          )
      )
    );
  });

  sendChangeDate$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.sendChangeDate),
      mergeMap(({ type, changeDate }) =>
        this.loanApplicationsService.sendChangeDate(changeDate).pipe(
          map(() => {
            return CreditLimitDetailsApiActions.sendChangeDateSuccess();
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.sendChangeDateFailure({
                error,
              })
            );
          })
        )
      )
    );
  });

  sendChangePortfolioAmount$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.sendChangePortfolioAmount),
      mergeMap(({ type, changeAmount }) =>
        this.loanApplicationsService.sendChangeAmount(changeAmount).pipe(
          map(() => {
            return CreditLimitDetailsApiActions.sendChangePortfolioAmountSuccess();
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.sendChangePortfolioAmountFailure({
                error,
              })
            );
          })
        )
      )
    );
  });

  openSnackBar$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        CreditLimitDetailsApiActions.sendDisbursementSuccess,
        CreditLimitDetailsApiActions.sendChangeDateSuccess,
        CreditLimitDetailsApiActions.sendEffectiveDateSuccess,
        CreditLimitDetailsApiActions.sendChangePortfolioAmountSuccess,
        CreditLimitDetailsApiActions.sendChangeAmountSuccess,
        CreditLimitDetailsApiActions.sendChangeEffectiveDateSuccess,
        CreditLimitDetailsApiActions.sendEarlyRepaymentRequestSuccess,
        CreditLimitDetailsApiActions.submitCreditLimitEarlyRepaymentSuccess,
        E2eLoanApiActions.savePayerBankAccountValidateSuccess
      ),
      concatLatestFrom(() => this.store.select(selectSnackBarMessage)),
      map(([type, snackBarData]) => {
        return TenantManagementLibPageActions.openSnackBar({ snackBarData });
      })
    );
  });

  navigateToApplications$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(
          CreditLimitDetailsApiActions.sendDisbursementSuccess,
          CreditLimitDetailsApiActions.sendChangeDateSuccess,
          CreditLimitDetailsApiActions.sendEffectiveDateSuccess,
          CreditLimitDetailsApiActions.sendChangePortfolioAmountSuccess,
          CreditLimitDetailsApiActions.sendChangeAmountSuccess,
          CreditLimitDetailsApiActions.sendChangeEffectiveDateSuccess,
          CreditLimitDetailsApiActions.sendEarlyRepaymentRequestSuccess,
          CreditLimitDetailsApiActions.submitCreditLimitEarlyRepaymentSuccess
        ),
        tap(() => {
          this.router.navigateByUrl('/operations/applications');
        })
      );
    },
    { dispatch: false }
  );

  getCollaterals$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.setCreditLimitId),
      concatLatestFrom(() => this.store.select(selectCreditLimitId)),
      mergeMap(([type, limitId]) =>
        this.creditLimitDetailsService.getCreditLimitCollaterals(limitId).pipe(
          map((creditLimitCollaterals) => {
            return CreditLimitDetailsApiActions.getCollateralsSuccess({
              creditLimitCollaterals,
            });
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.getCollateralsFailure({
                error,
              })
            );
          })
        )
      )
    );
  });

  createNewInsurance$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.createNewInsurance),
      mergeMap(({ newInsurance }) =>
        this.creditLimitDetailsService.createNewInsurance(newInsurance).pipe(
          map(() => {
            return CreditLimitDetailsApiActions.createNewInsuranceSuccess();
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.createNewInsuranceFailure({
                error,
              })
            );
          })
        )
      )
    );
  });

  savePayerBankAccountValidateSuccess$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(E2eLoanApiActions.savePayerBankAccountValidateSuccess),
      concatLatestFrom(() =>
        this.store.select(selectBorrowerCurrentPortfolioId)
      ),
      mergeMap(([type, portfolioId]) =>
        this.e2eService.getProducts(portfolioId).pipe(
          map((portfolioProducts) => {
            return CreditLimitDetailsApiActions.getBorrowerPortfolioProductsSuccess(
              {
                portfolioProducts,
              }
            );
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.getBorrowerPortfolioProductsFailure({
                error,
              })
            );
          })
        )
      )
    );
  });

  getFees$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.getFees),
      mergeMap(() =>
        this.creditLimitDetailsService.getFees().pipe(
          map((fees) => {
            return CreditLimitDetailsApiActions.getFeesSuccess({
              fees,
            });
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.getFeesFailure({
                error,
              })
            );
          })
        )
      )
    );
  });

  getOverview$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.getOverview),
      concatLatestFrom(() => this.store.select(selectCreditLimitId)),
      mergeMap(([{ type, effectiveDate, fees }, limitId]) =>
        this.creditLimitDetailsService
          .getOverview(limitId, effectiveDate as Date, fees)
          .pipe(
            map((overview) => {
              return CreditLimitDetailsApiActions.getOverviewSuccess({
                overview,
              });
            }),
            catchError((error: unknown) => {
              return of(
                CreditLimitDetailsApiActions.getOverviewFailure({
                  error,
                })
              );
            })
          )
      )
    );
  });

  sendEarlyRepaymentRequest$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.sendEarlyRepaymentRequest),
      mergeMap(({ portfolioIds, earlyRepaymentForm, fees }) =>
        this.creditLimitDetailsService
          .sendEarlyRepaymentRequest(portfolioIds, earlyRepaymentForm, fees)
          .pipe(
            map(() => {
              return CreditLimitDetailsApiActions.sendEarlyRepaymentRequestSuccess();
            }),
            catchError((error: unknown) => {
              return of(
                CreditLimitDetailsApiActions.sendEarlyRepaymentRequestFailure({
                  error,
                })
              );
            })
          )
      )
    );
  });

  sendSimulationRequest$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.sendSimulationRequest),
      mergeMap(({ earlyRepaymentSimulationRequest }) =>
        this.creditLimitDetailsService
          .sendSimulationRequest(earlyRepaymentSimulationRequest)
          .pipe(
            map(() => {
              return CreditLimitDetailsApiActions.submitCreditLimitEarlyRepaymentSuccess();
            }),
            catchError((error: unknown) => {
              return of(
                CreditLimitDetailsApiActions.submitCreditLimitEarlyRepaymentFailure(
                  {
                    error,
                  }
                )
              );
            })
          )
      )
    );
  });

  navigateToCreditLimit$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(CreditLimitDetailsApiActions.sendEarlyRepaymentRequestSuccess),
        // concatLatestFrom(() => this.store.select(selectCreditLimitId)),
        tap(() => {
          this.router.navigateByUrl(`/operations/credit-limits`);
        })
      );
    },
    { dispatch: false }
  );

  getLoanFullRepayments$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.getLoanFullRepayments),
      mergeMap(({ loanId, effectiveDate, fees }) =>
        this.creditLimitDetailsService
          .getFullRepaymentSimulate(loanId, effectiveDate, fees)
          .pipe(
            map((loanSimulatedRepayment) => {
              return CreditLimitDetailsApiActions.getCreditLimitSimulatedRepaymentsSuccess(
                {
                  loanSimulatedRepayment,
                }
              );
            }),
            catchError((error: unknown) => {
              return of(
                CreditLimitDetailsApiActions.getCreditLimitSimulatedRepaymentsFailure(
                  {
                    error,
                  }
                )
              );
            })
          )
      )
    );
  });

  getLoanPartialRepayments$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.getLoanPartialRepayments),
      mergeMap(
        ({ loanId, effectiveDate, fees, earlyRepaymentAmount, spreadMethod }) =>
          this.creditLimitDetailsService
            .getPartialRepaymentSimulate(
              loanId,
              effectiveDate,
              fees,
              earlyRepaymentAmount,
              spreadMethod
            )
            .pipe(
              map((loanSimulatedRepayment) => {
                return CreditLimitDetailsApiActions.getCreditLimitSimulatedRepaymentsSuccess(
                  {
                    loanSimulatedRepayment,
                  }
                );
              }),
              catchError((error: unknown) => {
                return of(
                  CreditLimitDetailsApiActions.getCreditLimitSimulatedRepaymentsFailure(
                    {
                      error,
                    }
                  )
                );
              })
            )
      )
    );
  });

  getLoanDebtRepayments$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.getLoanDebtRepayments),
      mergeMap(({ limitId, effectiveDate, amount }) =>
        this.creditLimitDetailsService
          .getDebtRepaymentSimulate(limitId, effectiveDate, amount)
          .pipe(
            map((loanSimulatedRepayment) => {
              return CreditLimitDetailsApiActions.getLoanDebtRepaymentsSuccess({
                loanSimulatedRepayment,
              });
            }),
            catchError((error: unknown) => {
              return of(
                CreditLimitDetailsApiActions.getLoanDebtRepaymentsFailure({
                  error,
                })
              );
            })
          )
      )
    );
  });

  openRepaymentsSimulation$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(
          CreditLimitDetailsApiActions.getCreditLimitSimulatedRepaymentsSuccess
        ),
        // concatLatestFrom(() => [
        //   this.store.select(selectProductDetails),
        //   this.store.select(selectBorrowerPrepayments),
        //   this.store.select(selectSelectedTrackId),
        //   this.store.select(selectTenantPortfolioTrackMode),
        // ]),
        tap(
          // ([
          //   { type },
          //   productDetails,
          //   prepayments,
          //   trackId,
          //   tenantPortfolioTrackMode,
          // ]) => {
          ({ type, loanSimulatedRepayment }) => {
            const loanRepaymentDetails: LoanRepaymentDetails = {
              loanDetails: loanSimulatedRepayment,
              loanPayments: [
                ...(loanSimulatedRepayment.disbursements || []),
                ...(loanSimulatedRepayment.payments || []),
              ],
              borrowerName: getBorrowerName(
                loanSimulatedRepayment.loanIdentityParties
              ),
              title: 'e2e.repaymentTable.repaymentsSimulationTable',
            };

            this.dialog.open(RepaymentsSimulationComponent, {
              disableClose: true,
              minWidth: '1050px',
              maxWidth: '95vw',
              data: loanRepaymentDetails,
              direction: this.languageService.getDirection(),
            });
          }
        )
      );
    },
    { dispatch: false }
  );

  openLoanDebtRepaymentSimulation$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(CreditLimitDetailsApiActions.getLoanDebtRepaymentsSuccess),
        tap(({ type, loanSimulatedRepayment }) => {
          this.dialog.open(DebtSimulationComponent, {
            disableClose: true,
            minWidth: '1050px',
            maxWidth: '95vw',
            data: loanSimulatedRepayment,
            direction: this.languageService.getDirection(),
          });
        })
      );
    },
    { dispatch: false }
  );

  getCreditLimitLoans$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CreditLimitDetailsPageActions.getCreditLimitLoans),
      concatLatestFrom(() => this.store.select(selectCreditLimitId)),
      mergeMap(([type, limitId]) =>
        this.creditLimitDetailsService.getCreditLimitLoans(limitId).pipe(
          map((creditLimitLoans) => {
            return CreditLimitDetailsApiActions.getCreditLimitLoansSuccess({
              creditLimitLoans,
            });
          }),
          catchError((error: unknown) => {
            return of(
              CreditLimitDetailsApiActions.getCreditLimitLoansFailure({
                error,
              })
            );
          })
        )
      )
    );
  });
}
